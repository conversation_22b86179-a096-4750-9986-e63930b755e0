cmake_minimum_required(VERSION 3.10)


include_directories(

	${CMAKE_CURRENT_SOURCE_DIR}/open/common_api/include
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/inc
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/inc/hal
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/inc/os
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/usb/inc
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/usb/ext_inc
	${CMAKE_CURRENT_SOURCE_DIR}/open/system/mrd/inc
	${CMAKE_CURRENT_SOURCE_DIR}/open/inc/at
    ${CMAKE_CURRENT_SOURCE_DIR}/open/system/utils/inc

# Begin Add by ycc, include dir
	${CMAKE_CURRENT_SOURCE_DIR}/xy/charger/inc
	${CMAKE_CURRENT_SOURCE_DIR}/xy/led/inc
	${CMAKE_CURRENT_SOURCE_DIR}/xy/key/inc
	${CMAKE_CURRENT_SOURCE_DIR}/xy/common
# End Add by ycc, include dir

    ${XROOT}/quectel/cust/inc
    
	${XROOT}/pcac/lwipv4v6/src/include
	${XROOT}/pcac/lwipv4v6/src/include/arch
	${XROOT}/pcac/lwipv4v6/src/include/lwip
	${XROOT}/pcac/lwipv4v6/src/include/ipv4
	${XROOT}/pcac/lwipv4v6/src/include/ipv6
	${XROOT}/pcac/lwipv4v6/src/include/netif
	
# Begin Add by ycc, include dir
	${XROOT}/hal/USIM/inc
# End Add by ycc, include dir
	${XROOT}/hal/PWM/inc
	${XROOT}/hal/MMU/inc
	${XROOT}/hal/usb3_dwc/inc
	${XROOT}/wlanhost/driver/uapapi/inc
	${XROOT}/softutil/aic_wifi/platform/wifi
	${XROOT}/softutil/aic_wifi/platform/inc
	${XROOT}/softutil/aic_wifi/host/driver/layer_abstract/inc
	${XROOT}/pcac/duster/inc
	${XROOT}/pcac/dial/inc
	${WROOT}/ci/inc
	${XROOT}/os/threadx/src/INT
	${XROOT}/hop/ssipc_mat/inc
	${XROOT}/hop/telephony/modem/inc
	${XROOT}/hal/QSPI_Flash/inc
	${XROOT}/hop/cpmu/inc
	${XROOT}/tavor/Arbel/inc
	${XROOT}/hal/sulog/inc
	${XROOT}/hop/telephony/atcmdsrv/inc
	${XROOT}/softutil/EEhandler/inc
	${XROOT}/softutil/flash/inc
	${XROOT}/softutil/EEhandler/src
	${XROOT}/softutil/softutil/inc
	${XROOT}/pcac/tr069/src
	${XROOT}/pcac/mrd/inc
	${XROOT}/ims/ims_client/src/external/mrvxml/inc
	${XROOT}/hop/RTC/inc
)

set(GROUP_NAME quectel)
set(GROUP_PACKAGE_LIST)

add_library(${GROUP_NAME})

package_include(app             quectel-app     GROUP_PACKAGE_LIST)
package_include(open/example    quectel-example GROUP_PACKAGE_LIST)
# Begin Add by ycc, xy-charger xy-led xy-key xy-at
package_include(xy/charger      xy-charger      GROUP_PACKAGE_LIST)
package_include(xy/led          xy-led          GROUP_PACKAGE_LIST)
package_include(xy/key          xy-key          GROUP_PACKAGE_LIST)
package_include(xy/at           xy-at           GROUP_PACKAGE_LIST)
# End Add by ycc, xy-charger xy-led xy-key xy-at
# Begin Add by ykx, xy_battery_soc
package_include(xy/battery_soc   xy-battery_soc   GROUP_PACKAGE_LIST)
# End Add by ykx, xy_battery_soc
if(${CONFIG_QUEC_PROJECT_SDK_ENABLE})

package_include(open/system      quectel-system GROUP_PACKAGE_LIST)

package_include(open/common_api  quectel-comm   GROUP_PACKAGE_LIST)

endif()

add_compile_definitions("$<$<OR:$<COMPILE_LANGUAGE:C>,$<COMPILE_LANGUAGE:CXX>>:${GROUP_DFLAGS}>")
add_compile_options("$<$<OR:$<COMPILE_LANGUAGE:C>,$<COMPILE_LANGUAGE:CXX>>:${GROUP_CFLAGS}>")


if(GROUP_PACKAGE_LIST)
        list(TRANSFORM GROUP_PACKAGE_LIST APPEND "_obj" OUTPUT_VARIABLE GROUP_PACKAGE_LIST_obj)
        add_dependencies(${GROUP_NAME} ${GROUP_PACKAGE_LIST})
        target_link_libraries(${GROUP_NAME} ${GROUP_PACKAGE_LIST_obj})
endif()

creat_prebuild_file_group(${GROUP_NAME} ${GROUP_PACKAGE_LIST})

