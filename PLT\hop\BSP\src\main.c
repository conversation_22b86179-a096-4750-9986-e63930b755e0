/*---------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/***************************************************************************
*               MODULE IMPLEMENTATION FILE
****************************************************************************
* Title: Initialing code
*
* Filename: main.c
*
* Target, platform: Tavor
*
* Authors: <AUTHORS>
*
* Description:
* 	Application_Initialize
*
*
* Last Updated:
*
* Notes:
******************************************************************************/
/*===========================================================================

                     INCLUDE FILES FOR MODULE

===========================================================================*/
#include "global_types.h"
#include <stdlib.h>
#include "asserts.h"
#include "platform.h"
#include "hal_cfg.h"
#include "nucleus.h"
#include "bsp_config.h"
#include "arbel.h"
#include "arm946e_s.h"
#include "commccu.h"
#include "mccu.h"
#include "apbccu.h"
#include "prm.h"
#include "csw_mem.h"
#include "i2c_eeprom.h"
#include "bsp.h"
#include "dsp_boot.h"
#include "IMEI.h"
#include "mep.h"
#include "main.h"
#include "simPin.h"
#include "utilities.h"
#include "loadTable.h"
#include "log.h"
#include "cgpio.h"
#include "FreqChange.h"
#ifdef PHS_SW_DEMO_TTC
#include "nvm_header.h"
#endif
//#include "HSI.h"
#include "ningbo.h"
#include "commpm_def.h"
#include "DVFM_ext.h"
#include "diag.h"
#include "diag_mem.h"
#include "ICAT_config.h"
#include "platform_nvm.h"
#include "crossPlatformSW.h"
#include "I2C_PMIC_Config.h"
#include "dma.h"
#include "timer.h"
#include "unibm.h"
#if !defined(TTC_BSP_ARM946_CP15)
#include "cpmu.h"
#include "tick_manager.h"
#include "NUtick.h"
#endif
#include "intc.h"
#include "bsp_config.h"
#include "pm_ext_debug.h"
#include "cpu_mips_test.h"
#include "bspUartManager.h"
#include "rtc.h"
#if !defined(TTC_BSP_ARM946_CP15)
#include "acipc.h"
#include "acipc_data.h"
#endif
#include "osa.h"
#include "watchdog.h"
#if defined(IPC_ENABLED)
#include "IPCComm.h"
#include "WS_IPCComm.h"
#endif
#include "EEHandler.h"
#if defined DATA_COLLECTOR_IMPL
#include "DataCollector.h"
#endif
#include "iml_nvm.h"
#include "I2C.h"
#include "miccoConfig.h"
#include "PMChip.h"
#include "bspLog.h"
#include "ReliableData.h"
#include "COMCfg.h"
#include "gpio.h"
#ifdef _USIM_ENABLED_
#include "usim.h"
#endif
#if !defined(TTC_BSP_ARM946_CP15)
#include "aam.h"
#endif
#if defined(__arm)
#include <locale.h>
#endif
#include <ctype.h>
#include "diag_comm_SD.h"
#include "Usb3DwcDefs.h"
#include "Usb3DwcMain.h"
#include "Usb3DwcDesc.h"
#include "pmic_rtc.h"
#ifdef CHG_SUPPORT
#include "charger.h"
#endif
// Begin Add by ykx
#include "battery_soc.h"
// End Add by ykx
/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for constants, macros, types,
variables and other items needed by this module.

===========================================================================*/

/* Init Task reference. */
OSATaskRef initTaskRef;

/* Init phase3 Task reference. */
OSATaskRef initPhase3TaskRef;

/* Init phase3 Task stack. */
void* InitPhase3Stack = NULL;

/* Init Task stack. */
static void* InitTaskStack = NULL;

/* Delete phase3 Task timer reference. */
OSATimerRef Phase3TDelTimer = NULL;

/* Platform cfg Semaphore. */
OSSemaRef PlatformCfgSemaRef;

/* Lte configuration. */
LTE_CONFIG_S LteCfg =
{
    CFG_PP_AUTO,
    MIPS_8_5,
    MIPS_3_0,
    LTE_DMA_208M,
    VOL_SETP_NO_SET,
    VOLTAGE_DEFAULT
};

/* Systwm debug configuration */
SYSDBGCfgDataS SysDbgCfg =
{
    _CPU_USAGE_DUMP_ENABLE,
    60,
    10000
};


#define NVM_HEAD_SET_STRING(header,field,value) \
   strncpy(header.field,value,sizeof(header.field)-1)

#ifdef PMU_SULOG_NLWG
PM_TYPE  gPMEnableFlag = _PM_DISABLE;
VCXO_TYPE gVCXOEnableFlag = VCXO_SD_DISABLE;
#else
PM_TYPE  gPMEnableFlag = _PM_ENABLE;
VCXO_TYPE gVCXOEnableFlag = VCXO_SD_ENABLE;
#endif
HWDFC_TYPE	gHWDFCEnableFlag = _HWDFC_DISABEL; //_HWDFC_DISABEL;

#ifndef ZEBU
IMLCfgDataS gIMLCfgdata = {SulogConfig_USB_JPIT_FRBD,0,{NULL}};//set L1_IML_CFG.
#else
IMLCfgDataS gIMLCfgdata = {SulogConfig_USB_JPIT,0,{NULL}};//set L1_IML_CFG.
#endif
SDLOG_TYPE SDLogEnableFlag = SD_LOG_DISABLE;

DbgFiltLvSt DbgFiltLvStData = {2,2,2,2,{NULL},{NULL}};

HWDFC_TEST_TYPE	gHWDFCTestFlag = _HWDFC_TEST_DISABEL;

/* ********** - begin */
L1AcatLog_TYPE gL1AcatLogEnableFlag = _L1_ACAT_LOG_DISABLE;
/* ********** - end */

/* Extra DDR Dump Flag */
UINT32 gExtraDDRDumpFlag = 0;

/* Platform configuration Data. */
PlatformCfgDataS PlatformCfg;

/* Default Log configuration */
Log_ConfigS log_config =
{
    ACAT_LOG_ENABLE,
    ACAT_MSG_ENABLE,
    RTI_LOG_ENABLE,
    ERR_RAMLOG_ENABLE
};

/* Backup Log configuration */
Log_ConfigS log_config_bak;

/* Usb driver configuration */
Usb_DriverS usbDrvCfg =
{
#ifndef MBIM_FUNCTION
#if defined (ENABLE_SLT_FEATURE)
    USB_MODEM_DIAG_DRIVER,
#else
    USB_ASR_MIFI_DRIVER,
#endif
    MASS_STORAGE_DISABLE,
    USB_AUTO_INSTALL_DISABLE,
    USB_OS_DETECT_ENABLE
#else // MBIM_FUNCTION
	USB_MBIM_GENERIC_DRIVER,
	MASS_STORAGE_DISABLE,
	USB_AUTO_INSTALL_DISABLE,
	USB_OS_DETECT_DISABLE
#endif // MBIM_FUNCTION
};

/* BIP configuration */
#ifdef BIP_FUNC_SUPPORT
Bip_ConfigS  BIPCfg = {BIP_ENABLE};
#endif

/* PMIC RTC Setting */
PMIC_RTC_Setting m_pmic_rtc_setting =
{
    0,
    0,
    DEFAULT_TIMEZONE
};

SulogCfgDataS SulogCfgDataDefault =
{
	SULOG_ENABLE_HW_SW,
	//{1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 2, 0, 1, 1, 1, 1},
	{0x4A656515, 0x55},
	//{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1},
	{0x45555515, 0x55},
	0
};


#ifdef YMODEM_EEH_DUMP
/* Ymodem Dump Cfg. */
Ymodem_Dump_type  YmodemDumpCfg = {YMODEM_ENABLE};

/* Ymodem Dump flag. */
static volatile int ymodemdumpflag = 0;
#endif

#ifdef WIFI_SUPPORT
/* WIFI power save parameter */
unsigned int WiFi_Power_Save_Parameter = 6500;
#endif

SULOG_ST SulogSetDefault;

UINT32 enable_cp_uart_log = 0xFFFFFFFF;

UINT32 enable_umc_acat_log = 0;

UINT32 Reset_Status = 0;

/* Watchdog reset flag */
unsigned int        WatchdogResetFlag = 0;

/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for types needed by this module.

===========================================================================*/

#if defined(LOAD_TABLE_RW_COPY_SUPPORTED)
typedef void (*LT_pVoidFuncVoid)(void);
#endif

#if defined(LOAD_TABLE_RW_COPY_SUPPORTED)
LT_pVoidFuncVoid  pF_diagAcatReadyExtAction;
LT_pVoidFuncVoid  pF_diagGenEventHandler;
#endif

/*===========================================================================

            EXTERN DEFINITIONS AND DECLARATIONS FOR MODULE

===========================================================================*/

/* RTI Config. */
extern RTICfg_t rtiConfig;

/* RNDIS configuration. */
extern RndisConfigS RndisCfg;

/* Default diag configuration */
extern diagCfgDataS diagCfg;

/* usim swap flag */
extern UINT32 usim_swap_flag;

#if defined(DIP_CHN)
/* Dip Channel control enable. */
extern BOOL DipChnCtrlEnbale;

/* DIP channels catalog Data */
extern DIP_CHN_CAT_NEW dip_chn_cats_new;

/* pll change cfg. */
extern Pll2ChgCfgDataS pll_change_cfg_base;
#endif


/*===========================================================================

                        EXTERN FUNCTION DECLARATIONS

===========================================================================*/



/*===========================================================================

                       INTERNAL FUNCTION DEFINITIONS

===========================================================================*/
/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Application_Initialize                                           */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function is called by NUCLEUS (INC_Initialize) in context   */
/*    of the init process. Must be supplied for every application using  */
/*    NUCLEUS.                                                           */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
VOID Application_Initialize(VOID *first_available_memory)
{
    OS_STATUS status;

    initState = BSP_INIT_CINIT2_PASSED;

    status = OSAInitialize();
    ASSERT(status == OS_SUCCESS);

	DynamicMemoryInit();

    commImageTableInit();

    /*Phase 1 (not OS based) initializations*/
	Phase1Inits();

    initState = BSP_INIT_PHASE1_PASSED;

    /* Allocate dynamic memory */
    InitTaskStack = malloc( INIT_STACK_SIZE );

    /* Run the system initialization task */
    memset(InitTaskStack, 0xA5, INIT_STACK_SIZE);

    status = OSATaskCreate(&initTaskRef,
                            InitTaskStack,
                            INIT_STACK_SIZE,
                            INIT_TASK_PRIORITY,
                            "InitTask",
                            InitTask,
                            NULL);
    ASSERT(status == OS_SUCCESS);

}/* End of <Application_Initialize> */

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      DeleteInitPhase2Task                                             */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function free the init task stack and delete the task(timer */
/*    call back function)                                                */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void DeleteInitPhase2Task(void)
{
/* This function is called (by mistake) from GPLC::L1BgInitialise()
 * Let's keep the call there and filler-function here.
 * For real delete let's use the DeleteInitPhase2TaskByTimer()
 */
}

/******************************************************************************
* Function     : DeleteInitPhase2TaskByTimer
*******************************************************************************
*
* Description  :
*
******************************************************************************/
void DeleteInitPhase2TaskByTimer(UINT32 id)
{
    extern OSATimerRef TimerDeleteInitTask;
    OSATaskDelete(initTaskRef);
    dynFree(InitTaskStack);
//#ifdef PLAT_TEST
//	OSATimerStop(TimerDeleteInitTask);	//Pijing Liu: TX timer cannot delete itself
//#else
    OSATimerDelete (TimerDeleteInitTask);
//#endif
}



/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for constants, macros, types,
variables and other items needed by this module.

===========================================================================*/






/*===========================================================================

            EXTERN DEFINITIONS AND DECLARATIONS FOR MODULE

===========================================================================*/
BOOL PlatformNvmHeaderValid(NVM_Header_ts *header)
{
    if ((strcmp(header->StructName, "PlatformCfgDataS") != 0)||
	    (strcmp(header->Version, "1.0") != 0)||
	    (strcmp(header->HW_ID, "TTC") != 0))
    {
        return FALSE;
    }
    else
    {
        return TRUE;
    }
}

UINT8 Sulog2SDCard(void)
{
	return SulogSetDefault.Sulog2SdCardFlag;
}

SULOG_TYPE_ID SulogType(void)
{
	return SulogSetDefault.logSwitch;
}

BOOL initSulogConfigToDSPDone = FALSE;
extern void CPASendSulogConfigToDSP(SULOG_ST SulogSet);
void InitSulogConfigToDSP(void)
{
    CPUartLogPrintf("sulog config:%d\r\n",SulogSetDefault.logSwitch);
	CPASendSulogConfigToDSP(SulogSetDefault);
    initSulogConfigToDSPDone = TRUE;
}

void SulogCfgUpdateSetting(SULOG_ST SulogSet, int saved)
{
    if(SulogSet.logSwitch == SulogCfgDataDefault.logSwitch)
    {
        CPASendSulogConfigToDSP(SulogSet);
    }

    if(saved)
    {
        SulogCfgDataDefault.logSwitch = SulogSet.logSwitch;
        SulogCfgDataDefault.Sulog2SdCardFlag = SulogSet.Sulog2SdCardFlag;
        if(SulogSet.Sulog2SdCardFlag)
        {
            SulogCfgDataDefault.PrintLevelForSdCardEnable = SulogSet.PrintLevel;
        }
        else
        {
            SulogCfgDataDefault.PrintLevelForSdCardDisable = SulogSet.PrintLevel;
        }

        /* Platform Cfg Mutex Lock */
        PlatformCfgMutexLock();

        if(!PlatformCfgReadFromNvm())
        {
             /* Set default platform configuration */
            PlatformCfgSetDefaultSetting();
        }

        /* Sulog Cfg Data */
        memcpy(&(PlatformCfg.SulogCfg), &SulogCfgDataDefault, sizeof(SulogCfgDataS));

        /* Save platform configuration to NVM file. */
        PlatformCfgSaveToNvm();

        /* Platform Cfg Mutex Unlock */
        PlatformCfgMutexUnlock();

    }

}

void SetIMLSettingToNVM(IMLCfgDataS *IMLCfgdata)
{
    return;
}

#define REG32(x)	*((volatile unsigned long*)(x))

void GetIMLSettingFromNVM(IMLCfgDataS *IMLCfgdata){
	uart_printf("GetIMLSettingFromNVM need to do.......................................\r\n");
}

void save_usim_swap_flag(UINT32 val)
{
	UINT8 *pBuf = NULL;

	/* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    usim_swap_flag = val;

    pBuf = PlatformCfg.CCfg.buf;
    pBuf[0] &= ~0x1;
    pBuf[0] |= (val & 0x1);

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();
}


UINT32 read_usim_swap_flag(void)
{
    UINT8 *pBuf = NULL;
	UINT32 default_val = 0;

#ifdef CONFIG_KESTREL_A0
	extern UINT32 getPmicInfo(void);

	if(getPmicInfo())
	{
		default_val = 1; //for notion, default sim slot is slot 2;
	}
#endif

    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(PlatformCfgReadFromNvm())
    {
        pBuf = PlatformCfg.CCfg.buf;

        if(0 == default_val)
        {
            if((pBuf[0] & 0x1) == 1)
            {
                default_val = 1;
            }
        }
        else
        {
            if((pBuf[0] & 0x1) == 0)
            {
                default_val = 0;
            }
        }
    }

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    return default_val;

}


#define __REG(x)        *(volatile unsigned long*)(x)
#define GPIO112_ADDERSS 0xD401E2A0
#define GPIO78_ADDERSS 	0xD401E2E0
#define GPIO_3_PDR()     __REG(0xD401910C)     /* GPIO pin direction register */
#define GPIO_3_PSR(n)     __REG(0xD4019118)     /* GPIO pin output set register */
#define PULL_UP_C          0xC000  //Enable pull up resistor
#define PULL_DN_C          0xA000  //Enable pull down resistor
#define DRV_SLOW_C         0x0800  //Use slow drive strength
#define DRV_MED_C          0x1000  //Use medium drive strength
#define DRV_FAST_C         0x1800  //Use fast drive strength
#define AF0_C              0X0000  //Alternate function 0
#define AF1_C              0X0001  //Alternate function 1
#define AF2_C              0X0002  //Alternate function 2
#define AF3_C              0X0003  //Alternate function 3
#define AF4_C              0X0004  //Alternate function 4
#define AF5_C              0X0005  //Alternate function 5
#define AF6_C              0X0006  //Alternate function 6
#define AF7_C              0X0007  //Alternate function 7
#define RESERVED_C         0x00C0  //RESERVED_C bits that must be set

void MSAInit()
{
	volatile unsigned long temp;
	UMTS_Chip_Version	Local_Umts_Chip_Version;
	BspCustomType customtype;
	customtype=bspGetBoardType();

#if 1 //yqian
    *(volatile unsigned long *)0xD025009C = 0xC0;
#endif
    // CCGR & ACGR
    if(AC_IS_SHM)   //disable BBSD and MSALPEN
    {
	    *(volatile unsigned long *)0xD02500C4 &= ~((1<<25)|(1<<14)); //disable BBSD and MSASLPEN
    }
    temp = *(volatile unsigned long *)0xD0250034; //CP hardware clock gating
    temp |= 0x77FFFFFF;
    *(volatile unsigned long *)0xD0250034 = temp;

	*(volatile unsigned long *)0xD02500C8 = 0xFFFFFFFF;

    *(volatile unsigned long *)0xD0250068 = 0x00FFC7FF; //CPSS clock gating
    *(volatile unsigned long *)0xD025005C = 0x0; //set GB clock as 416M@PLL1 for default

    // enable I2S clock
    *(volatile unsigned long *)0xd4050040 = 0xF820130B;
    *(volatile unsigned long *)0xd4050044 = 0xF820130B;

    // AIB CLK enable
    *(volatile unsigned long *)0xD401503C = 0x3;

    // GPIO CLK enable
    *(volatile unsigned long *)0xD4015008 = 0x3;

#ifdef PLAT_AQUILAC
   *(volatile unsigned long *)0xD4090100 |= 0x1<<27; //APB_spare_reg1, DIV2_EN, KEY for clock

   *(volatile UINT32*)0xD401E138 = 0xA0C7; //set CP_GPO_0 as function7, GPIO0, for RESET_N.
   *(volatile UINT32*)0xD401E13C = 0xA0C7; //set CP_GPO_1 as function7, GPIO3, for DIGRF_EN.
#else //Nezha3
    temp = *(volatile UINT32*)0xD401E0C8; //dummy read to ensure the register programming
    *(volatile UINT32*)0xD401E0C8 = 0xB0C1; //set pin "slave_reset_out" as GPIO122 for rf_reset func.
#endif
}

UINT8 MSA_LOADER_BIN[] = {
	0x03,0xc0,0x00,0x18,0x24,0x00,0x3c,0xe1,0x00,0x00,
	0x3d,0xe1,0x00,0x00,0x48,0xe1,0xff,0xd0,0x08,0xe1,
	0x00,0xff,0x40,0x91,0x40,0x34,0x51,0xe1,0xe0,0xd1,
	0x11,0xe1,0x00,0x00,0x49,0xe1,0x01,0x00,0x09,0xe1,
	0x00,0x40,0x00,0x9c,0xa2,0xe0,0x02,0x10,0x03,0xc8,
	0x00,0x18,0x08,0x9e,0x00,0x9c,0x72,0xe2,0x64,0x62,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
#define MSA_LOADER_BIN_LEN	0x80

char* APP_BIN = (char*)0x07f00000;
#define APP_BIN_LEN 0x50000

void AIB_Secure_Write(unsigned int address, unsigned int value)
{
	BU_REG_WRITE(0xd4015050, 0xBABA);
	BU_REG_WRITE(0xd4015054, 0xEB10);
	BU_REG_WRITE(address, value);
}

UINT32 AIB_Secure_Read(unsigned int address)
{
	UINT32 val;
	BU_REG_WRITE(0xd4015050, 0xBABA);
	BU_REG_WRITE(0xd4015054, 0xEB10);
	val = BU_REG_READ(address);
	return val;
}

#define DTCM_ADDRESS 0xa1000000
extern UINT32  Image$$DDR_BX2_RO$$Base;
#define DSP_DDR_ADDRESS ((UINT32)(&Image$$DDR_BX2_RO$$Base))
#define ITCM_ADDRESS 0xf0200000
#define write32(addr, val)		( *((volatile unsigned  long *) (addr)) = val )
#define read32(addr)		( *((volatile unsigned  long *) (addr)) )


#if defined(CFG_BOOTP_BOOTBX2)
//bx2 binary are included in cr5 binary!!!
#define REG_CP_PMU_CPRR (0xD02500c0)
#ifdef CFG_CP_INCLUDE_BX2_BIN
/* bx2 code and data must be aligned */
unsigned char bx2_code[] __attribute__((section(".bin_data")));
unsigned char bx2_data[] __attribute__((section(".bin_data")));
#include "bx2_code.h"
#include "bx2_data.h"
#endif // end of CFG_CP_INCLUDE_BX2_BIN

struct  img_header_t {
    unsigned int img_id;
    unsigned int img_len;
    unsigned int img_addr;
    unsigned int img_reserved;
};

void cp_copy_bx2_bin(char core_id)
{
  int i;
  unsigned src_addr, dst_addr;
  unsigned ptcm_addr, dtcm_addr;
  unsigned boot_addr_cfg; //config reg for external boot address
  unsigned bx2_clk_res_ctrl; //config reg for external boot address
  struct img_header_t *bx2_img;

  switch (core_id) {
    case BX2_C:
      ptcm_addr = 0xA1200000;
      dtcm_addr = 0xA1000000;
      boot_addr_cfg = 0xA0010114;
      LogMsg(0,"try to boot BX2_C\n");
      break;
    case BX2_U0:
      ptcm_addr = 0xA2A00000;
      dtcm_addr = 0xA2800000;
      boot_addr_cfg = 0xA0010414;
     bx2_clk_res_ctrl = 0xA0010120;
     LogMsg(0,"try to boot BX2_U0\n");
     break;
   case BX2_U1:
     ptcm_addr = 0xA3200000;
     dtcm_addr = 0xA3000000;
     boot_addr_cfg = 0xA0010514;
     bx2_clk_res_ctrl = 0xA0010124;
     LogMsg(0,"try to boot BX2_U1\n");
     break;
   case BX2_D0:
     ptcm_addr = 0xA1A00000;
     dtcm_addr = 0xA1800000;
     boot_addr_cfg = 0xA0010214;
     bx2_clk_res_ctrl = 0xA0010128;
     LogMsg(0,"try to boot BX2_D0\n");
     break;
   case BX2_D1:
     ptcm_addr = 0xA2200000;
     dtcm_addr = 0xA2000000;
     boot_addr_cfg = 0xA0010314;
     bx2_clk_res_ctrl = 0xA001012c;
     LogMsg(0,"try to boot BX2_D1\n");
     break;
   default:
     trace("invalid core_id:%d\n",core_id);
     StopTest(FAIL);
     break;
 }
 //set clock and reset ctrl for bx2, esp for bx2c
 if(core_id!=BX2_C) {
   REG32(bx2_clk_res_ctrl) |= 0x81;
   REG32(bx2_clk_res_ctrl) |= (0x5<<1);
   REG32(bx2_clk_res_ctrl) |= (0x1<<8);
   unsigned read_data = REG32(bx2_clk_res_ctrl);
   while (read_data & 0x100) {
     read_data = REG32(bx2_clk_res_ctrl);
   }
   REG32(bx2_clk_res_ctrl) |= (0x1<<9);
   REG32(bx2_clk_res_ctrl) |= (0x1<<10);
 }

 write32(dtcm_addr+BX2_CORE_ID,core_id); //mark core id

 //copy bx2 code. must be 4 byte alined when write ptcm!!!
 bx2_img=(struct img_header_t *)bx2_code;
 src_addr=(unsigned)bx2_img+sizeof(struct img_header_t);
 if(bx2_img->img_addr<BX2_DDR_START_ADDR)
   dst_addr=ptcm_addr+bx2_img->img_addr; //means in ptcm
 else
   dst_addr=bx2_img->img_addr;
 if(bx2_img->img_addr) {
   write32(boot_addr_cfg,bx2_img->img_addr);
   trace("boot externally @0x%x.(0x%x=0x%x)\n",dst_addr,boot_addr_cfg,read32(boot_addr_cfg));
 }
 LogMsg(0,"image magic:0x%08x\n",bx2_img->img_id);
 LogMsg(0,"image len:0x%08x\n",bx2_img->img_len);
 LogMsg(0,"image addr:0x%08x\n",dst_addr);
 for(i=0;i<=bx2_img->img_len/4;i++) {
   write32(dst_addr+i*4,read32((unsigned)src_addr+i*4));
 }
 LogMsg(0, "bx2 code copy is done\n");
  //copy bx2 data
  bx2_img=(struct img_header_t *)bx2_data;
  src_addr=(unsigned)bx2_img+sizeof(struct img_header_t);
  if(bx2_img->img_addr<BX2_DDR_START_ADDR)
    dst_addr=dtcm_addr+bx2_img->img_addr; //means in dtcm
  else
    dst_addr=bx2_img->img_addr;
  LogMsg(0,"image magic:0x%08x\n",bx2_img->img_id);
  LogMsg(0,"image len:0x%08x\n",bx2_img->img_len);
  LogMsg(0,"image addr:0x%08x\n",dst_addr);
  memcpy((void *)dst_addr, (void *)src_addr, bx2_img->img_len);
  LogMsg(0, "bx2 data copy is done\n");

  REG32(bx2_clk_res_ctrl) |= (0x1<<11); //release bx2 core from reset!
}

void cp_boot_bx2(char core_id)
{
    /* boot BX2. */
#define REG_CP_PMU_CPRR_CPR      (1 << 0)
#define REG_CP_PMU_CPRR_APR      (1 << 1)
#define REG_CP_PMU_CPRR_DSPR     (1 << 2)
#define REG_CP_PMU_CPRR_BBR      (1 << 3)
#define REG_CP_PMU_CPRR_WDTR     (1 << 4)
#define REG_CP_PMU_CPRR_DSRAMINT (1 << 5)

    /* copy bx2 img to target place */
    cp_copy_bx2_bin(core_id);

    /* a must for all dsp release */
    REG32(REG_CP_PMU_CPRR) &= ~(BIT_8|REG_CP_PMU_CPRR_DSPR | REG_CP_PMU_CPRR_BBR | REG_CP_PMU_CPRR_DSRAMINT);
    while (REG32(REG_CP_PMU_CPRR) & (BIT_8|REG_CP_PMU_CPRR_DSPR|REG_CP_PMU_CPRR_BBR|REG_CP_PMU_CPRR_DSRAMINT));
    LogMsg(0,"BX2 is released from reset!\n");

    guiCoreState |= (1<<core_id);
}
#endif

#define cp15_dis_mpu() /*SCTLR*/                                              \
{                                                                                   \
    unsigned val;                                                                 \
    asm __volatile__ ("MRC p15, 0, %0, c1, c0, 0;" \
                      "BIC %0, %0, #0x1;"          \
                      "DSB;"                       \
                      "MCR p15, 0, %0, c1, c0, 0;"  \
                      "ISB;"                       \
                      :"=r"((val)));              \
}

#define cp15_dis_mpu_1() /*SCTLR*/                                              \
{                                                                                   \                                                            \
    asm __volatile__ ("MRC p15, 0, %0, c1, c0, 0;" \
                      "BIC %0, %0, #0x1;"          \
                      "DSB;"                       \
                      "MCR p15, 0, %0, c1, c0, 0;"  \
                      "ISB;");              \
}

#define REG_CP_PMU_CPRR_CPR      (1 << 0)
#define REG_CP_PMU_CPRR_APR      (1 << 1)
#define REG_CP_PMU_CPRR_DSPR     (1 << 2)
#define REG_CP_PMU_CPRR_BBR      (1 << 3)
#define REG_CP_PMU_CPRR_WDTR     (1 << 4)
#define REG_CP_PMU_CPRR_DSRAMINT (1 << 5)
#define BIT_8 (1 << 8)
void copyDSPITCM(void);

void releaseBX2()
{
     //int i = 0;
    //seems if mpu enabled,dsp boot has issue
     //LogMsg(0, "disable mpu\n");

    //unsigned clk = *(volatile unsigned long*)(0xd0250064);
    //cp15_dis_mpu();
    //cleart bi4-bit0
    //clk &=   ~(0xf);
   // clk |= (0x1 << 3) | (0x1 << 5);
   // *(volatile unsigned long*)(0xd0250064) = clk;
   // *(volatile unsigned long*)(0xd0250180) |= 1 << 10;

  //  while((*(volatile unsigned long*)(0xd0250064) & (0x1 << 5)) == (0x1 << 5));
    //*(volatile  unsigned long *)(0xa0010114) = 0x10;
   // *(volatile unsigned long*)(0xa0010000) = 0x1;
#if 0

   *(volatile unsigned long*)(0xd4015000+0xa0) = 0x1; //enable ipc clk
    *(volatile unsigned long*)(0xd4070030) &= ~0x1;
    fatal_printf("reg %x = 0x%x\n", 0xd4070030, *(volatile unsigned long*)(0xd4070030));
    *(volatile unsigned long*)(0xd4050020) &= ~(BIT_8|REG_CP_PMU_CPRR_DSPR | REG_CP_PMU_CPRR_BBR | REG_CP_PMU_CPRR_DSRAMINT);
    while (*(volatile unsigned long*)(0xd4050020) & (BIT_8|REG_CP_PMU_CPRR_DSPR|REG_CP_PMU_CPRR_BBR|REG_CP_PMU_CPRR_DSRAMINT));

    *(volatile unsigned long*)(0xd4015000+0xa0) = 0x1; //enable ipc clk
    //fatal_printf("%s,line:%d,0x%lx\r\n",__func__,__LINE__,*(volatile UINT32*)(0xd4015000+0xa0));
    fatal_printf("yizhou2 BX2 and msa is released from reset!\n");
    //for(i=0;i< 10;i++)
        //fatal_printf("reg 0x%x = 0x%x\n", (0x107c0000),*(volatile unsigned long*)(0x107c0000));
#else
    fatal_printf("0xd4051060 value:0x%lx",*(volatile UINT32*)0xd4051060);
    *(volatile UINT32*)0xd4051060 = 0x2000;
    copyDSPITCM();
    *(volatile unsigned long*)(0xd4015000+0xa0) = 0x1; //enable ipc clk
    *(volatile unsigned long*)(0xd4070030) &= ~0x1; //BX2 boot from interal, ptcm
    *(volatile unsigned long*)(0xd4070034)  = 0x10; //BX2 boot address
    //fatal_printf("reg %x = 0x%x\n", 0xd4070030, *(volatile unsigned long*)(0xd4070030));

    *(volatile unsigned long*)(0xd4050020) &= ~(BIT_8|REG_CP_PMU_CPRR_DSPR | REG_CP_PMU_CPRR_BBR | REG_CP_PMU_CPRR_DSRAMINT);
    while (*(volatile unsigned long*)(0xd4050020) & (BIT_8|REG_CP_PMU_CPRR_DSPR|REG_CP_PMU_CPRR_BBR|REG_CP_PMU_CPRR_DSRAMINT));
    // *(volatile unsigned long*)(0xd4050020) &= ~REG_CP_PMU_CPRR_DSRAMINT;


    *(volatile unsigned long*)(0xd4015000+0xa0) = 0x1; //enable ipc clk
    fatal_printf("%s,line:%d,0x%lx\r\n",__func__,__LINE__,*(volatile UINT32*)(0xd4015000+0xa0));
    fatal_printf("BX2 and msa is released from reset!%x\n", *(volatile unsigned long*)(0xd4050020));
    //for(i=0;i< 10;i++)
        //fatal_printf("reg 0x%x = 0x%x\n", (0x107c0000),*(volatile unsigned long*)(0x107c0000));
#endif

}

void copyDSPITCM(void)
{
    UINT32 i = 0;
    UINT32 dstAddr;
    UINT32 srcAddr;
    //REG32(BX2_SUBSYS_REG_BASE + 0x0) = 0x1;

    fatal_printf("no bit 2 %s,line:%d-%x:%x\r\n",__func__,__LINE__, ITCM_ADDRESS, DSP_DDR_ADDRESS);
    //fatal_printf("no bit 2 %x\r\n", *(volatile UINT32*)(DSP_DDR_ADDRESS));
    //fatal_printf("no bit 2 %s,line:%d\r\n",__func__,__LINE__);


    //fatal_printf("no bit 2 %s,line:%d\r\n",__func__,__LINE__);
    //*(volatile unsigned long*)(REG_CP_PMU_CPRR) |= (1 << 8);  //reset DSP

    dstAddr = ITCM_ADDRESS;
    srcAddr = DSP_DDR_ADDRESS;
    fatal_printf("DSP status:%x\r\n",*(volatile unsigned long*)(0xd4050020));
    //*(volatile unsigned long*)(0xd4050020) |= REG_CP_PMU_CPRR_DSPR;
    //*(volatile unsigned long*)(0xd4050020) = REG_CP_PMU_CPRR_DSRAMINT;
    *(volatile unsigned long*)(0xd4050020) |= (REG_CP_PMU_CPRR_DSPR|REG_CP_PMU_CPRR_BBR);
    *(volatile unsigned long*)(0xd4050020) |= REG_CP_PMU_CPRR_DSRAMINT;
    *(volatile unsigned long*)(0xd4050020) &= ~(REG_CP_PMU_CPRR_DSPR|REG_CP_PMU_CPRR_BBR);
    fatal_printf("noCopy bit 2 %s,line:%d\r\n",__func__,__LINE__);
    //*(volatile UINT32*)(ITCM_ADDRESS) = 0xFF;
    //while(1){uart_printf("End\r\n");OSATaskSleep(200);}
    for(i=0;i< 1024*128/4;i++) {
       *(volatile UINT32*)(dstAddr+i*4) = *(volatile UINT32*)(srcAddr+i*4);
    }
    //for(i=0;i<64;i++)
     //   uart_printf("0x%lx\r\n",*(volatile UINT32*)(srcAddr+i*4));
    //while(1){uart_printf("End\r\n");OSATaskSleep(200);}
    uart_printf("End\r\n");

}

void BX2Set(void)
{
    UINT32 i = 0;
    UINT32 dstAddr;
    UINT32 srcAddr;

	fatal_printf("no bit 2 %s,line:%d jyz\r\n",__func__,__LINE__);
	#if 1
    dstAddr = ITCM_ADDRESS;
    srcAddr = DSP_DDR_ADDRESS;
	fatal_printf("%s,line:%d\r\n",__func__,__LINE__);
    for(i=0;i< 1024*128/4;i++) {
       *(volatile UINT32*)(dstAddr+i*4) = *(volatile UINT32*)(srcAddr+i*4);
    }
    #endif

	fatal_printf("%s,line:%d\r\n",__func__,__LINE__);
}


void releaseDSP(void)
{
    releaseBX2();

    fatal_printf("%s,line:%d\r\n",__func__,__LINE__);

    rf_uart_test_entry();//should be after DSP released.
}

 void AIBAuxInit(void)
 {
	 volatile unsigned long val;

	 val = AIB_Secure_Read(0xd401e800); //AFE
	 val &= ~(0x3 << 6);
	 AIB_Secure_Write(0xd401e800, val);

	 val = AIB_Secure_Read(0xd401e804); //ANT
	 val &= ~(0x3 << 6);
	 AIB_Secure_Write(0xd401e804, val);

	 val = AIB_Secure_Read(0xd401e814); //GSM
	 val &= ~(0x3 << 6);
	 AIB_Secure_Write(0xd401e814, val);

	 val = AIB_Secure_Read(0xd401e830); //WB_IO
	 val &= ~(0x3 << 6);
	 AIB_Secure_Write(0xd401e830, val);
 }


 /******************************************************************************
 * Function     : Phase1Inits
 *******************************************************************************
 *
 * Description  :  Perform phase1 inits
 *                 Called from Application_Initialize only !!!
 *                Evaluates the HW initialization and database initialization of every package
 * 				  Note : at this stage the NUCLEUS is not running
 ******************************************************************************/

extern BOOL is_ram_fs;
static void LittleDelay(UINT32 num)
{
	while(num--)
	{;}
}

void Phase1Inits (void)
{
    UINT32 Rstatus_temp = 0;
	BspCustomType customtype;

#ifndef EDEN_1928
	customtype = bspGetBoardType();
	Reset_Status = (*(volatile UINT32 *)0xd4050028);

	Rstatus_temp=(Reset_Status & 0xe00) >> 9;

	if(Rstatus_temp >= 7)
	{
		Rstatus_temp = 0;
	}
	else
	{
		Rstatus_temp++;
	}

	*(volatile UINT32 *)0xd4050028=(Reset_Status&(~0xe00))|(Rstatus_temp<<9);
#endif


#ifdef GPIO_TRACE
#ifndef EDEN_1928
    initGPIO();
#endif
#endif

#if defined(__arm)
	 /* Ensure proper functionality of some C library functions */
    setlocale(LC_ALL, "C");
#endif

#if defined(IPC_ENABLED)
	IPCCreateEvent();
#endif

    /* Initialzies the Power Management*/
	CPMUPhase1Init();

#if !defined(TTC_BSP_ARM946_CP15)
   /* all the modules that uses timers must be before the ipc init */

	RMPhase1Init();
    AAMPhase1Init();
#endif

	INTCPhase1Init();            /* Initializes interrupt controller HW and database*/

   	timerPhase1Init();           /* Initializes timers controlller HW and database*/

	GpioPhase1Init();

	/* IPC package : HW initialization and database initializatio */
#if defined(IPC_ENABLED)
	{
	    IPC_Config initialIPCConfig;
	    initialIPCConfig.msgWorkMode = IPC_WM_NOTIFICATION;

		IPCCommPhase1Init(&initialIPCConfig);
	}
#endif

    SetDiagMemFns(mallocExt, free);

	diagPhase1Init(); /* Initializes diag package HW and database*/

	crossPlatformSWPhase1Init();  /* Initializes cross Platform SW database*/

    watchdogPhase1Init();

	I2CPhase1Init(); /* Initializes I2C package HW and database*/
	RIPCPhase1Init();

	TickPhase1Init(); /*Add Tick Manager initialization */

#ifdef _UART_ENABLED_
    bspUartPhase1Init();
#endif

#if defined(_USIM_ENABLED_)
   	USIMPhase1Init(); /* Initializes USIM package HW and database*/
#endif

	RTCPhase1Init();

	MEPPhase1Iinit();

}


 /******************************************************************************
 * Function     : Phase2Inits
 *******************************************************************************
 *
 * Description  :  Perform phase2 inits
 *                 Including the OS object for every package
 *                 Called from the Init Task context
 ******************************************************************************/

extern UINT32  Image$$DDR_RFBIN_RO$$Base;
#define GRIFFIN_Z1_VERSION 0x00020000
#define GRIFFIN_Z2_VERSION 0x00020001
#define MAX_RF_VER_STR_LEN  128 /* max 128 char for rf bin cfg version */

UINT32 griffin_ver = 0x00020000;
UINT8 rf_por_release_ver[MAX_RF_VER_STR_LEN];

BOOL isGriffinZ1(void)
{
    return (griffin_ver == GRIFFIN_Z1_VERSION);
}

BOOL isGriffinZ2(void)
{
    return (griffin_ver == GRIFFIN_Z2_VERSION);
}
#if 0
typedef struct
{
	UINT32 cbSize;// size of file header;
	UINT32 signature;//'R','F','B','N'
	UINT32 compilerVersion;// Ver x.x.x.(db_ver)
	UINT32 chunkOffset;//address:0x200 * 32bit; size 0x200 * 32bit; data:max 0xFF000 bytes
	UINT32 chunkBlocks; // data item blocks: max 0x200 items
	UINT32 chunkSize;//address:0x200 * 32bit+ size 0x200 * 32bit + data:max 0xFF000 bytes
	UINT32 chipsetSubType;// rfTypeId (low word);rfTypes max (high word)
	UINT32 reserve4;// reserve
	UINT32 reserve3;// reserve
	UINT32 reserve2;// reserve
	UINT32 reserve1;// reserve
	UINT32 reserve0;// reserve
	UINT8 compileTime[32];//26 chars for compile date,format:Wed Jan 02 02:03:55 1980\n\0
	UINT8 authorName[32];// max 32 char for author name
	UINT8 cfgReleaseName[128];// max 128 char for rf bin cfg version
	UINT8 chipsetSubTypeName[32];//max 32 bytes
}rf_bin_hdrType;
#endif
typedef struct
{
	UINT8 reserved[204];
	UINT8 RF_BINARY_VERSION_NAME[MAX_RF_VER_STR_LEN];
}rf_bin_hdrType;

void GetRfInfo(void)
{
    UINT32 length;
    UINT32 rfbin_addr = ((UINT32)(&Image$$DDR_RFBIN_RO$$Base));
    rf_bin_hdrType *rfHdr = (rf_bin_hdrType*)rfbin_addr;
	volatile char *ptr, *begin;

	//if(CHIP_IS_KESTREL_G2_Z1)
    //	griffin_ver = 0x20001;

	begin = (volatile char*)&rfHdr->RF_BINARY_VERSION_NAME[0];
	ptr = begin;
	length = MIN(strlen((const char *)ptr),MAX_RF_VER_STR_LEN-1);

	if(length >0)
	{
		memcpy(rf_por_release_ver, (const char *)begin, length);
		rf_por_release_ver[length] = '\0';
	}
	else
	{
	    fatal_printf("The RF Bin Version is unavailable!\r\n");
	}
}

void PrintRfPorVer(void)
{
    DIAG_FILTER(Diag,Utils,PrintRfPorVer,DIAG_INFORMATION)
    diagPrintf("RF POR Version is %s", rf_por_release_ver);
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformSetResetMagicNum                                         */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function Set reset magic number.                            */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformSetResetMagicNum(void)
{
    /* Check watchdog reset flag. */
	WatchdogResetFlag = *(volatile UINT32 *)DDR_WATCHDOG_RST_ADDR;

	if((WatchdogResetFlag & 0xFFFF)==0xFACE)
	{
        if((WatchdogResetFlag & 0xFFFF0000)==0xBABE0000)
        {
            CPUartLogPrintf("Last reset: Factory reset, caller: 0x%x", *(volatile UINT32 *)DDR_WATCHDOG_RST_CALLER_ADDR);
        }
        else
        {
            CPUartLogPrintf("Last reset: Watchdog reset, caller: 0x%x", *(volatile UINT32 *)DDR_WATCHDOG_RST_CALLER_ADDR);
        }
	}

	*(volatile UINT32 *)DDR_WATCHDOG_RST_ADDR = 0x00;
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Phase3TDelTimerCallback                                          */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function delete phase3 init task.                           */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void Phase3TaskDelTimer(UINT32 id)
{
    if(initPhase3TaskRef != NULL)
    {
        OSATaskDelete(initPhase3TaskRef);
        initPhase3TaskRef = NULL;
    }

    if(InitPhase3Stack != NULL)
    {
        free(InitPhase3Stack);
        InitPhase3Stack = NULL;
    }

    OSATimerDelete (Phase3TDelTimer);
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Phase3Task                                                       */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function do platform phase3 initialization.                 */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void Phase3Task(void *argv)
{
    CPUartLogPrintf("Enter phase3");

    audioServerPhase2Init();

	lwip_init_all();

#ifdef ETHERNET_SUPPORT
	emac_init();
#endif

	init_load_dsp();

#ifndef REMOVE_FOTA
	init_atfota();
#endif

#ifndef NOIMS
	ims_rtos_init();
#endif

    MbimServiceInit();

#ifdef PPP_ENABLE
	modem_init();
#endif

	duster_module_init();

#ifndef NO_DIALER
	dialer_task_init();
#endif

	CM_init();

#ifdef LPA_SUPPORT
	//lpa_init();
#endif

#ifdef NTP
	NTP_Init();
#endif

#ifdef WEBUI_SUPPORT
	init_mongoose();
	MrvInitCgi();
	Web_SMS_task_init();
#endif

#ifdef BIP_FUNC_SUPPORT
	BipCmdTaskInit();
#endif

	uart_atcmd_init();

#ifdef AT_OVER_AP_UART
    uart_atcmd_ap_uart_init();
#endif

#ifndef NO_EXTEND_MY_Q_AT
    /* atnet initialize. */
	init_atnet();
#endif

#ifdef TR069_SUPPORT
    /* tr069 initialize. */
    tr069_init();
#endif

#ifdef CMUX_ENABLE
    /* Init Cmux service */
    cmux_init();
#endif

#ifdef CTCC_DM_SMS
    telecom_SMS_init();
#endif

#ifdef CTCC_DM_HTTP
    telecom_init();
#endif

#ifdef CMCC_DM
    dm_init();
#endif

#ifdef CUCC_DM
    cucc_dmp_init();
    cucc_at_init();
#endif

#ifdef TSEN_SUPPORT
    tsen_threshold_detect_init();
#endif

#ifdef QUECTEL_PROJECT_CUST
    extern void quecos_system_start(void);
    quecos_system_start();
	
	extern void Set_SWversion_From(int sw_from);
    Set_SWversion_From(2);

// Begin Add by ycc
    extern int xy_pwrkey_init(void);
    extern int xy_charger_init(void);
    extern int xy_led_init(void);
    extern int xy_key_init(void);
    extern int xy_async_init(void);
    xy_pwrkey_init();
    xy_charger_init();
    xy_led_init();
    xy_key_init();
    xy_async_init();
// End Add by ycc

// Begin Add by ykx
    battery_soc_init();
// End Add by ykx

#endif
    INTCEnable(INTC_SRC_PMIC); //moved from PMIC_INT_init()

    CPUartLogPrintf("Exit phase3");

    OSATimerCreate(&Phase3TDelTimer);
    OSATimerStart(Phase3TDelTimer, 1000, 0, Phase3TaskDelTimer, 0);
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Phase3Inits                                                      */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function do platform phase3 initialization.                 */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void Phase3Inits(void)
{
    OS_STATUS status;

    /* init ATCmdSvr Phase1. */
    initATCmdSvrPhase1();

    /* Allocate the stack memory for Phase3 task. */
    InitPhase3Stack = malloc( INIT_STACK_SIZE ) ;

    /* Create the Phase3 task. */
    status = OSATaskCreate(&initPhase3TaskRef,
                            InitPhase3Stack,
                            INIT_STACK_SIZE,
                            INIT_TASK_PRIORITY,
                            "Phase3T",
                            Phase3Task,
                            NULL);
    ASSERT(status == OS_SUCCESS);

    CPUartLogPrintf("Phase3 Init");
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Phase2Inits                                                      */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function do platform phase2 initialization.                 */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      sATP                                modem point                  */
/*      para                                modem parameters             */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void Phase2Inits (void)
{
    OSA_STATUS status;
    BspCustomType customtype = bspGetBoardType();

    /* Set reset magic number.*/
    PlatformSetResetMagicNum();

#if !defined(TTC_BSP_ARM946_CP15)
    //Remote file is not accessible.
    //Try to load permanent "PlatformNvm_ts* p_PlatformNvm" buffer from a given buffer
    //(it may be already corrected by file-server).
    //Update from real file later.
#ifdef PHS_SW_DEMO_TTC_PM
    CPMUPhase2Init();
#endif

#ifdef PHS_SW_DEMO_TTC_PM
    RMPhase2Init();
#endif
#endif

	INTCPhase2Init();

	timerPhase2Init();

	/* Configure timers to have OS tick */
	InitTimers();

    /* Configure 13MHz time counter with 32kHz and performance_count */
	initTimerCount13MHz();

    /* Gpio Phase2 Init. */
	GpioPhase2Init();

    /* Qspi DMA Init */
	qspi_dma_init();

#ifndef PHS_SW_DEMO_TTC
	DMAPhase2Init();
#endif

	/* Add registration to the tick manager*/
	NUTickRegister();

	/* Configure  Timer for Tick Manager interrupts*/
	TickPhase2Init();

	/* Init PMIC ID. */
	PMIC_Init_ID();

    /* Flash and File system initialization */
    FDI2FileSysInit();

#if defined(RELIABLE_DATA)
    /* Must be before COMCfgPhase2Init */
    ReliableDataPrePhase1();
#endif

#ifdef PSM_ENABLE
    psm_lowlevel_init();
#endif

	/* init dual sim type in early stage, PS will get dual sim type */
   	initDualSimType();

#if defined(RELIABLE_DATA)
   	ReliableDataPhase2();
#endif

    /* Get platform setting from NVM file. */
    PlatformCfgGetSetting();

    /* PMIC initialize*/
    PMIC_Init();

#ifdef QUECTEL_PROJECT_CUST
    extern void PMIC_LDO_GMAC_Power(void);
    PMIC_LDO_GMAC_Power();
#endif

#ifdef CHG_SUPPORT
    chargerManagePhase2Init();
#endif

    RIPCPhase2Init();

    watchdogPhase2Init();

#if defined(EE_HANDLER_ENABLE)
    eeHandlerPhase2Init();  //read cnfg file.
#endif

#if defined(EE_HANDLER_ENABLE)
    eeHandlerPhase3Init(); //read cnfg file.
#endif

#if defined(EE_HANDLER_ENABLE)
    eeWdtMgrInit();
#endif

	/* if there is no COMCfg.csv and Platform.nvm file, create a default file. */
	PlatformNvm_CreatePlatformNVMFile();

    /* init the UART package*/
	bspUartPhase2Init();

#ifndef QUECTEL_PROJECT_CUST 
	sdcard_init();
#endif /*QUECTEL_PROJECT_CUST*/

    Usb3DwcInitialize();

    initAtpID();

	initChannelInfo();

    UniBMInit();

    IpNetUpLinkInit();

    if(1 == getBmTestMode())
    {
        createAcipcBmTestTask();
    }

    GetRfInfo();

    pF_diagAcatReadyExtAction = TestScriptExecFromLoadTable;
    pF_diagGenEventHandler    = TestScriptExecFromLoadTable;

#if !defined(TTC_BSP_ARM946_CP15)
    //Init LOCAL configuration from default file into permanent "PlatformNvm_ts* p_PlatformNvm" buffer
    PlatformNvm_Init(NULL, NULL);
#ifdef _UART_ENABLED_
    bspUartPhase3Init(); /* update to platformNvm */
#endif
#endif //!defined(TTC_BSP_ARM946_CP15)

#if !defined(TTC_BSP_ARM946_CP15)
    PlatformNvm_Print();
#endif /*(TTC_BSP_ARM946_CP15)*/

	MEPPhase2Iinit();    // !!!!!   Must be before ReliableDataPhase2   !!!!!!

	UdpInit();

    /* Initializes the COM platfrom configuration information */
	COMCfgPhase2Init();
	//Note: In some cases COMCfgPhase2Init sets initState=BSP_INIT_PHASE2_PASSED. however, even if changed it is restored before returning.
	/*****Attention: All modules who needs COMCfg services must be initialized after this line*****/

#if defined(_USIM_ENABLED_)
	USIMResourceInit();
	USIMPhase2Init();
#endif

#ifdef EDEN_1928
	PMCPhase2Init();
#endif

#if !defined(TTC_BSP_ARM946_CP15)
	PMCNvmInit();
#ifdef _EPROM_EXIST_
  {	EEPROM_ReturnCode   eprom_rc;
    eprom_rc = EPROMi2cDisplayBoardInfo();
    if ( eprom_rc == EEPROM_RC_OK )
         EPROMi2cCheckCardEcn();
  }
#endif

    initState = BSP_INIT_PHASE2_BASIC_PASSED;

/********* End of BASIC platform init & config; start "applications" *********/
#endif /*(TTC_BSP_ARM946_CP15)*/

#if defined (IPC_ENABLED)
	IPCCommPhase2Init();
    // Check ROM-Mask before the IPC init
    // since if mask is wrong the IPCCommPhase3Init() may never return
#endif

    if(StrtupIsPowerup())
    {
        simPin_invalidate(0);
		simPin_invalidate(1);
	}

/*for TTC IPC phase 3 init need to be addressed upon full system build */

#if defined (ENABLE_CHIP_SCREEN)|| defined (ENABLE_SLT_FEATURE)
	chipScreenInit();
#endif

    rti_rt_init();

	sdio_init();

    Phase3Inits();

    initState = BSP_INIT_PHASE2_PASSED;
} /* Phase2Inits */

void bspSwResetReasonSet(UINT8 rr)
{
    return;
}

BOOL isCpuUsageDumpEnabled(void)
{
	return (SysDbgCfg.cpuUsageDumpEnable == _CPU_USAGE_DUMP_ENABLE)?TRUE : FALSE;
}

void SetCpuUsageDumpEnable(BOOL enable)
{
    if(enable)
    {
        SysDbgCfg.cpuUsageDumpEnable = _CPU_USAGE_DUMP_ENABLE;
    }
    else
    {
        SysDbgCfg.cpuUsageDumpEnable = _CPU_USAGE_DUMP_DISABLE;
    }
}

UINT32 getCpuDumpInterval(void)
{
	return SysDbgCfg.dumpInterval;
}

UINT32 getCpuTaskPriority(void)
{
	return SysDbgCfg.cpuUsageTaskPriority;
}

BOOL is_start_comm_delay(void)
{
    return PlatformCfg.CommonCfg.start_comm == START_COMM_DELAY_ON;
}

//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,platNvmPmuFlagRead
void platNvmPmuFlagRead(void)
{
    FILE_ID         fd;

    fd = FDI_fopen(PLATFORM_CFG_FILE, "rb");
    if (fd != 0)
    {
        FDI_fseek(fd, sizeof(NVM_Header_ts), SEEK_SET);

        /* read structure */
        FDI_fread((void *)(&PlatformCfg), sizeof(PlatformCfgDataS), 1, fd);

        if (PlatformCfg.HslCfg.PMCfgVal == _PM_ENABLE)
        {
            DIAG_FILTER(SW_PLAT, NVM, platNvmPmuFlagRead1, DIAG_INFORMATION)
            diagPrintf("Current PMCfgVal flag: _PM_ENABLE");
        }
        else
        {
            DIAG_FILTER(SW_PLAT, NVM, platNvmPmuFlagRead2, DIAG_INFORMATION)
            diagPrintf("Current PMCfgVal flag: _PM_DISABLE");
        }

        /* close file */
        FDI_fclose(fd);
    }
}


//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,platNvmPmuEnable
void platNvmPmuEnable(void)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    gPMEnableFlag = _PM_ENABLE;
    gVCXOEnableFlag = VCXO_SD_ENABLE;

    PlatformCfg.HslCfg.PMCfgVal   = _PM_ENABLE;
    PlatformCfg.HslCfg.VCXOCfgVal = VCXO_SD_ENABLE;

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    DIAG_FILTER(SW_PLAT, NVM, platNvmPmuEnable, DIAG_INFORMATION)
    diagPrintf("PMCfgVal is set as _PM_ENABLE");

}


//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,platNvmPmuDisable
void platNvmPmuDisable(void)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    gPMEnableFlag = _PM_DISABLE;
    gVCXOEnableFlag = VCXO_SD_DISABLE;

    PlatformCfg.HslCfg.PMCfgVal   = _PM_DISABLE;
    PlatformCfg.HslCfg.VCXOCfgVal = VCXO_SD_DISABLE;

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    DIAG_FILTER(SW_PLAT, NVM, platNvmPmuDisable, DIAG_INFORMATION)
    diagPrintf("PMCfgVal is set as _PM_DISABLE");

}


UINT32 GetExtraDDRDumpFlag(void)
{

	DIAG_FILTER(SW_PLAT, NVM, GetExtraDDRDumpFlag, DIAG_INFORMATION)
	diagPrintf("GetExtraDDRDumpFlag:%d", gExtraDDRDumpFlag);

	return gExtraDDRDumpFlag;
}

//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,ExtraDDRDumpFlagRead
void ExtraDDRDumpFlagRead(void)
{
    FILE_ID         fd;

    fd = FDI_fopen(PLATFORM_CFG_FILE, "rb");
    if (fd != 0)
    {
        FDI_fseek(fd, sizeof(NVM_Header_ts), SEEK_SET);

        /* read structure */
        FDI_fread((void *)(&PlatformCfg), sizeof(PlatformCfgDataS), 1, fd);

        if (PlatformCfg.HslCfg.ExtraDDRDumpFlag == 1)
        {
            DIAG_FILTER(SW_PLAT, NVM, ExtraDDRDumpFlagRead1, DIAG_INFORMATION)
            diagPrintf("ExtraDDRDumpFlag: ENABLE");
        }
        else
        {
            DIAG_FILTER(SW_PLAT, NVM, ExtraDDRDumpFlagRead2, DIAG_INFORMATION)
            diagPrintf("ExtraDDRDumpFlag: DISABLE");
        }

        /* close file */
        FDI_fclose(fd);
    }
}

//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,ExtraDDRDumpFlagEnable
void ExtraDDRDumpFlagEnable(void)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    PlatformCfg.HslCfg.ExtraDDRDumpFlag = 1;

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    DIAG_FILTER(SW_PLAT, NVM, ExtraDDRDumpFlagEnable, DIAG_INFORMATION)
    diagPrintf("ExtraDDRDumpFlag is set as ENABLE");
}

//ICAT EXPORTED FUNCTION - SW_PLAT,NVM,ExtraDDRDumpFlagDisable
void ExtraDDRDumpFlagDisable(void)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    PlatformCfg.HslCfg.ExtraDDRDumpFlag = 0;

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    DIAG_FILTER(SW_PLAT, NVM, ExtraDDRDumpFlagDisable, DIAG_INFORMATION)
    diagPrintf("ExtraDDRDumpFlag is set as DISABLE");
}


/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      setRndisPacketType                                               */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function set Rndis Packet Type.                              */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void setRndisPacketType(BOOL type)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    RndisCfg.RndisMultiplePacket = type;

    /* Diag config */
	memcpy(&(PlatformCfg.RndisCfg), &RndisCfg, sizeof(RndisConfigS));

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    DIAG_FILTER(SW_PLAT, NVM, ExtraDDRDumpFlagDisable, DIAG_INFORMATION)
    diagPrintf("ExtraDDRDumpFlag is set as DISABLE");
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgMutexInit                                             */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function initialize Platform Cfg mutex.                      */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgMutexInit(void)
{
	OSA_STATUS status;

    if(!PlatformCfgSemaRef)
    {
    	status = OSASemaphoreCreate (&PlatformCfgSemaRef, 1, OSA_FIFO);
    	ASSERT(status == OS_SUCCESS);
	}
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgMutexLock                                             */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function do Platform Cfg mutex lock.                         */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgMutexLock(void)
{
	OSA_STATUS status;

	if(!PlatformCfgSemaRef)
    {
    	status = OSASemaphoreCreate (&PlatformCfgSemaRef, 1, OSA_FIFO);
    	ASSERT(status == OS_SUCCESS);
	}

	status = OSASemaphoreAcquire(PlatformCfgSemaRef, OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgMutexUnlock                                           */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function do Platform Cfg mutex unlock.                       */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgMutexUnlock(void)
{
	OSA_STATUS status;

	status = OSASemaphoreRelease(PlatformCfgSemaRef);
	ASSERT(status == OS_SUCCESS);
}


/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgReadNvm                                               */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function read platform configuration from NVM file.          */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
BOOL PlatformCfgReadFromNvm(void)
{
    BOOL valid = FALSE;
#if defined (INTEL_FDI)
	FILE_ID fd;
	NVM_Header_ts header;

	fd = FDI_fopen(PLATFORM_CFG_FILE, "rb");
	if (fd != 0)
	{
		/* Read structure header */
		FDI_fread((void *)(&header), sizeof(NVM_Header_ts), 1, fd);
		if (!PlatformNvmHeaderValid((&header)))
	    {
	        /* close file */
		    FDI_fclose(fd);

	        CPUartLogPrintf("!!Warning: Invalid Platform_Cfg.nvm");
            FDI_remove(PLATFORM_CFG_FILE);

            /* It is invalid file */
            valid = FALSE;
	    }
	    else
	    {
    		/* read structure */
    		FDI_fread((void *)(&PlatformCfg), sizeof(PlatformCfgDataS), 1, fd);

    		/* close file */
    		FDI_fclose(fd);

            /* Check the validity of Tail guard */
    		if(PlatformCfg.TailGuard == PLATFORM_CFG_GUARD)
    		{
                /* It is valid file */
        		valid = TRUE;
    		}
    		else
    		{
                CPUartLogPrintf("!!Wrong Tail 0x%x, Invalid Platform_Cfg.nvm", PlatformCfg.TailGuard);
                FDI_remove(PLATFORM_CFG_FILE);

                /* It is invalid file */
                valid = FALSE;
    		}
		}
	}
	else
	{
        /* It is invalid file */
        valid = FALSE;
	}
#endif

	return valid;
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgSaveToNvm                                             */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function save platform configuration to NVM file.            */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgSaveToNvm(void)
{
#if defined (INTEL_FDI)
	FILE_ID fd;
	NVM_Header_ts header;

	/* write diag cfg */
	fd = FDI_fopen(PLATFORM_CFG_FILE, "wb");
	if (fd != 0)
	{
		/* write structure header */
		header.StructSize = sizeof(PlatformCfgDataS);
		header.NumofStructs = 1;
		NVM_HEAD_SET_STRING(header,StructName,"PlatformCfgDataS");
		NVM_HEAD_SET_STRING(header,Date,"");
		NVM_HEAD_SET_STRING(header,time,"");
		NVM_HEAD_SET_STRING(header,Version,"1.0");
		NVM_HEAD_SET_STRING(header,HW_ID,"TTC");
		NVM_HEAD_SET_STRING(header,CalibVersion,"");

		FDI_fwrite(&header, sizeof(NVM_Header_ts), 1, fd);

		/* write structure */
		FDI_fwrite((void*)&PlatformCfg, sizeof(PlatformCfgDataS), 1, fd);

		/* close file */
		FDI_fclose(fd);
	}
#endif

}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgSetDefaultSetting                                     */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function set default platform configuration.                 */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgSetDefaultSetting(void)
{
    /* HSL Cfg Data */
    PlatformCfg.HslCfg.PMCfgVal         = gPMEnableFlag;
	PlatformCfg.HslCfg.VCXOCfgVal       = gVCXOEnableFlag;
	PlatformCfg.HslCfg.HWDFCCfgVal      = gHWDFCEnableFlag;
	PlatformCfg.HslCfg.HWDFCTestCfgVal  = gHWDFCTestFlag;
	PlatformCfg.HslCfg.SDLogCfgVal      = SD_LOG_DISABLE;
	PlatformCfg.HslCfg.L1AcatLogVal     = gL1AcatLogEnableFlag; /* CQ0003TTTT */
    memcpy(&(PlatformCfg.HslCfg.IMLCfgdata), &gIMLCfgdata, sizeof(gIMLCfgdata));
    memcpy(&(PlatformCfg.HslCfg.DbgFiltLvData), &DbgFiltLvStData, sizeof(DbgFiltLvStData));

	PlatformCfg.HslCfg.ExtraDDRDumpFlag = gExtraDDRDumpFlag;

    /* Log Cfg Data */
    memcpy(&(PlatformCfg.LogCfg), &log_config, sizeof(Log_ConfigS));

    /* Usb driver Cfg Data */
    memcpy(&(PlatformCfg.usbDrvCfg), &usbDrvCfg, sizeof(Usb_DriverS));

    /* LTE Cfg Data */
    memcpy(&(PlatformCfg.LteCfg), &LteCfg, sizeof(LTE_CONFIG_S));

    /* Sulog Cfg Data */
    memcpy(&(PlatformCfg.SulogCfg), &SulogCfgDataDefault, sizeof(SulogCfgDataS));

    CPUartLogPrintf("sulog Disable:0x%x,0x%x",
        SulogCfgDataDefault.PrintLevelForSdCardDisable.value32[0],
        SulogCfgDataDefault.PrintLevelForSdCardDisable.value32[1]
        );

    CPUartLogPrintf("sulog Enable:0x%x,0x%x",
        SulogCfgDataDefault.PrintLevelForSdCardEnable.value32[0],
        SulogCfgDataDefault.PrintLevelForSdCardEnable.value32[1]
        );

    /* RTI Cfg Data */
    memcpy(&(PlatformCfg.rtiConfig), &rtiConfig, sizeof(RTICfg_t));

    /* Init default EE Cfg Flag. */
    eeInitDfltConfiguration();

    /* EE Cfg Data */
    memcpy(&(PlatformCfg.eeCfg), &eeConfiguration, sizeof(EE_Configuration_t));

    /* Diag config */
    memcpy(&(PlatformCfg.diagCfg), &diagCfg, sizeof(diagCfgDataS));

    /* Ymodem Cfg Data */
    memcpy(&(PlatformCfg.YmodemCfg), &YmodemDumpCfg, sizeof(Ymodem_Dump_type));

    /* System debug Cfg Data */
    memcpy(&(PlatformCfg.SysDbgCfg), &SysDbgCfg, sizeof(SYSDBGCfgDataS));

    /* RNDIS Cfg Data */
	memcpy(&(PlatformCfg.RndisCfg), &RndisCfg, sizeof(RndisConfigS));

#ifdef BIP_FUNC_SUPPORT
    memcpy(&(PlatformCfg.bipCfg), &BIPCfg, sizeof(Bip_ConfigS));
#endif

    /* PMIC RTC Setting */
    memcpy(&(PlatformCfg.rtcSetting), &m_pmic_rtc_setting, sizeof(PMIC_RTC_Setting));

    /* Changeable Cfg Data. */
    memset(&(PlatformCfg.CCfg), 0x00, sizeof(CCfg_ConfigS));

    /* Common Cfg Data. */
    memset(&(PlatformCfg.CommonCfg), 0x00, sizeof(CommonCfgDataS));

	PlatformCfg.CommonCfg.uart_log_cfg  = UART_LOG_OFF;
	PlatformCfg.CommonCfg.umc_log_cfg   = UMC_LOG_OFF;
	PlatformCfg.CommonCfg.usim_io_cfg   = USIM_IO_MIDDLE;
	PlatformCfg.CommonCfg.start_comm    = START_COMM_DELAY_OFF;

	if(PlatformCfg.CommonCfg.uart_log_cfg == UART_LOG_ON)
	{
		enable_cp_uart_log = 1;
	}
	else
	{
		enable_cp_uart_log = 0;
    }

	if(PlatformCfg.CommonCfg.umc_log_cfg == UMC_LOG_ON)
	{
		EnableUmcAcatLog();
	}
	else
	{
		DisableUmcAcatLog();
    }

    /* USIM IO cfg. */
	usim_io_cfg_set((UINT32)(PlatformCfg.CommonCfg.usim_io_cfg));

#if defined(DIP_CHN)
	/* DIP channels catalog Data */
	memcpy(&(PlatformCfg.DipChnCfg.Dip_chn), &dip_chn_cats_new, sizeof(DIP_CHN_CAT_NEW));

    /* Enable manual mode for special case(such as test SIMcards, no SIM card). */
	PlatformCfg.DipChnCfg.resv = 2;

	/* Set DipChnCtrlEnbale to TRUE. */
    DipChnCtrlEnbale = TRUE;

	/* Pll change Cfg. */
	memcpy(&(PlatformCfg.Pll2Cfg), &pll_change_cfg_base, sizeof(Pll2ChgCfgDataS));
#endif

    /* Dcxo Control Cfg. */
	PlatformCfg.DcxoCtrl.RfDcxoIsUsingFlg = 1;  // dcxo function is enable by default
#ifdef PLAT_KAGU
	PlatformCfg.DcxoCtrl.RfDcxoIsUsingFlg = 0;  //KAGU dcxo function is disable by default
#endif
#ifdef PLAT_LARK
	PlatformCfg.DcxoCtrl.RfDcxoIsUsingFlg = 0;  //LARK dcxo function is disable by default
#endif
	PlatformCfg.DcxoCtrl.useExternalTemp  = 0;  //default use internal sensor

    /* Tail guard */
    PlatformCfg.TailGuard = PLATFORM_CFG_GUARD;
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      PlatformCfgGetSetting                                            */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function get platform configuration from NVM file.           */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void PlatformCfgGetSetting(void)
{
    PlatformCfgMutexInit();

#if defined (INTEL_FDI)
	if(PlatformCfgReadFromNvm())
    {
        /* HSL Cfg Data */
        gPMEnableFlag           = PlatformCfg.HslCfg.PMCfgVal;
        gHWDFCTestFlag		    = PlatformCfg.HslCfg.HWDFCTestCfgVal;
        gVCXOEnableFlag         = PlatformCfg.HslCfg.VCXOCfgVal;
        SDLogEnableFlag         = PlatformCfg.HslCfg.SDLogCfgVal;
        gL1AcatLogEnableFlag    = PlatformCfg.HslCfg.L1AcatLogVal; /* ***********/
        memcpy(&gIMLCfgdata, &(PlatformCfg.HslCfg.IMLCfgdata), sizeof(gIMLCfgdata));

        gExtraDDRDumpFlag       = PlatformCfg.HslCfg.ExtraDDRDumpFlag;
        memcpy(&DbgFiltLvStData, &(PlatformCfg.HslCfg.DbgFiltLvData), sizeof(DbgFiltLvStData));

		/* Log Cfg Data */
		memcpy(&log_config, &(PlatformCfg.LogCfg), sizeof(Log_ConfigS));

        /* Get rti cfg. */
		if(PlatformCfg.LogCfg.rti_cfg == RTI_LOG_DISABLE)
		{
            rtiDynamicSwitch = FALSE;
		}
		else
		{
            rtiDynamicSwitch = TRUE;
		}

		/* Usb driver Cfg Data */
		memcpy(&usbDrvCfg, &(PlatformCfg.usbDrvCfg), sizeof(Usb_DriverS));

        /* LTE Cfg Data */
		memcpy(&LteCfg, &(PlatformCfg.LteCfg), sizeof(LTE_CONFIG_S));

		/* Sulog Cfg Data */
		memcpy(&SulogCfgDataDefault, &(PlatformCfg.SulogCfg), sizeof(SulogCfgDataS));

		/* RTI Cfg Data */
		memcpy(&rtiConfig, &(PlatformCfg.rtiConfig), sizeof(RTICfg_t));

        /* Init RTI mode from NVM file. */
		if(rti_current_mode == rti_mode_none)
		{
		    /* Initialize RTI mode from NVM file .*/
            rti_current_mode = rtiConfig.rtiMode;
		}

		/* EE Cfg Data */
		memcpy(&eeConfiguration, &(PlatformCfg.eeCfg), sizeof(EE_Configuration_t));

		/* Diag config */
		memcpy(&diagCfg, &(PlatformCfg.diagCfg), sizeof(diagCfgDataS));

		/* Ymodem Cfg Data */
		memcpy(&YmodemDumpCfg, &(PlatformCfg.YmodemCfg), sizeof(Ymodem_Dump_type));

		/* System debug Cfg Data */
		memcpy(&SysDbgCfg, &(PlatformCfg.SysDbgCfg), sizeof(SYSDBGCfgDataS));

		/* RNDIS Cfg Data */
        memcpy(&RndisCfg, &(PlatformCfg.RndisCfg), sizeof(RndisConfigS));

        /* Bip config */
#ifdef BIP_FUNC_SUPPORT
        memcpy(&BIPCfg, &(PlatformCfg.bipCfg), sizeof(Bip_ConfigS));
#endif

        /* PMIC RTC Setting */
		memcpy(&m_pmic_rtc_setting, &(PlatformCfg.rtcSetting), sizeof(PMIC_RTC_Setting));

        /* Common Cfg data */
		if(PlatformCfg.CommonCfg.uart_log_cfg == UART_LOG_ON)
		{
            enable_cp_uart_log = 1;
		}
		else
		{
            enable_cp_uart_log = 0;
        }

		if(PlatformCfg.CommonCfg.umc_log_cfg == UMC_LOG_ON)
		{
			EnableUmcAcatLog();
		}
		else
		{
			DisableUmcAcatLog();
        }

        /* USIM IO cfg. */
		usim_io_cfg_set((UINT32)(PlatformCfg.CommonCfg.usim_io_cfg));

#if defined(DIP_CHN)
		/* DIP channels catalog Data */
        memcpy(&dip_chn_cats_new, &(PlatformCfg.DipChnCfg.Dip_chn), sizeof(DIP_CHN_CAT_NEW));

        /* Set DipChnCtrlEnbale according to the resv value. */
        DipChnCtrlEnbale = (PlatformCfg.DipChnCfg.resv == 0) ? FALSE: TRUE;

        /* Pll change config. */
        memcpy(&pll_change_cfg_base, &(PlatformCfg.Pll2Cfg), sizeof(Pll2ChgCfgDataS));
#endif
	}
	else
	{
	    /* Set default platform configuration */
	    PlatformCfgSetDefaultSetting();

        /* Save platform configuration to NVM file. */
		PlatformCfgSaveToNvm();
	}

	DIAG_FILTER(SW_PLAT, NVM, DUMP, DIAG_INFORMATION)
	diagStructPrintf("DIAG Configuartion: %S{HSLCfgDataS}", &(PlatformCfg.HslCfg), sizeof(HSLCfgDataS));

	SulogSetDefault.logSwitch        = SulogCfgDataDefault.logSwitch;
	SulogSetDefault.Sulog2SdCardFlag = SulogCfgDataDefault.Sulog2SdCardFlag;

	if(SulogSetDefault.Sulog2SdCardFlag)
	{
		SulogSetDefault.PrintLevel = SulogCfgDataDefault.PrintLevelForSdCardEnable;
	}
	else
	{
		SulogSetDefault.PrintLevel = SulogCfgDataDefault.PrintLevelForSdCardDisable;
	}

	memcpy(&log_config_bak, &log_config, sizeof(Log_ConfigS));
#endif

}


#ifdef YMODEM_EEH_DUMP
void setYmodemdumpflag(int flag)
{
    ymodemdumpflag = flag;
}
#endif

//ICAT EXPORTED FUNCTION - AAA,BBB,EnableUmcAcatLog
void EnableUmcAcatLog(void)
{
	enable_umc_acat_log=1;
}

//ICAT EXPORTED FUNCTION - AAA,BBB,DisableUmcAcatLog
void DisableUmcAcatLog(void)
{
	enable_umc_acat_log=0;
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      useYmodem                                                        */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function get ymodem flag.                                   */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      size                                Memory size                  */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
BOOL useYmodem(void)
{
#ifdef YMODEM_EEH_DUMP
    if ((ymodemdumpflag == 1) &&
        (YmodemDumpCfg.config == YMODEM_ENABLE))
    {
        return TRUE;
    }
	else
#endif
	{
	    return FALSE;
    }
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      getYmodemConfig                                                  */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This function get ymodem config.                                 */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      size                                Memory size                  */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
int getYmodemConfig(void)
{
#ifdef YMODEM_EEH_DUMP
    return YmodemDumpCfg.config;
#else
    return 0;
#endif
}

/*===========================================================================

FUNCTION DumpFileToSDEnable

DESCRIPTION
  Check whether we dump assert bin to SD card is enable or not.

DEPENDENCIES
  none

RETURN VALUE
  return staus

SIDE EFFECTS
  none

===========================================================================*/
BOOL DumpFileToSDEnable(void)
{
#ifdef FAT32_FILE_SYSTEM
    if (sdcard_is_ready())
    {
        return TRUE;
    }
	else
#endif
	{
        return FALSE;
	}

}

#ifdef BIP_FUNC_SUPPORT
int BIPCfgSet(int type)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

	if(!PlatformCfgReadFromNvm())
	{
         /* Set default platform configuration */
	    PlatformCfgSetDefaultSetting();
    }

    if (type == BIP_ENABLE)
    {
        BIPCfg.bipctrl = BIP_ENABLE;
	}
    else
    {
        BIPCfg.bipctrl = BIP_DISABLE;
    }

    /* BIP Cfg Data */
	memcpy(&(PlatformCfg.bipCfg), &BIPCfg, sizeof(Bip_ConfigS));

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

	return 0;
}

int BIPCfgGet(void)
{
    return BIPCfg.bipctrl;
}
#endif

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      set_mifi_log                                                     */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function configure log option from para.                     */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void set_mifi_log(int para1, int backup)
{
    char log = 0;
    char msg = 0;
    char rti = 0;
    char ramlog = 0;

    /* backup: (0: no backup), (1: back up), (2: restore) */

/*------------------------------------------------------------------------*/

    log = (para1 & 0xF000) >> 12;
    msg = (para1 & 0xF00) >> 8;
    rti = (para1 & 0xF0) >> 4;
    ramlog = para1 & 0x0F;

    log_config.log_cfg = (Log_ConfigE)log;
    log_config.msg_cfg = (Msg_ConfigE)msg;
    log_config.rti_cfg = (RTI_ConfigE)rti;
    log_config.ramlog = (RamLog_ConfigE)ramlog;
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      LogConfigure                                                     */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function configure log option and save to nvm.               */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void LogConfigure(char log, char msg, char rti, char ramlog)
{
    if(log <= (char)ACAT_LOG_ENABLE)
    {
        log_config.log_cfg  = (Log_ConfigE)log;
    }

    if(msg <= (char)ACAT_MSG_ENABLE)
    {
        log_config.msg_cfg  = (Msg_ConfigE)msg;
    }

    if(rti <= (char)RTI_LOG_ENABLE)
    {
        log_config.rti_cfg  = (RTI_ConfigE)rti;
    }

    if(ramlog <= (char)USB_RAMLOG_ENABLE)
    {
        log_config.ramlog  = (RamLog_ConfigE)ramlog;
    }

    /* Log Cfg Data */
    memcpy(&(PlatformCfg.LogCfg), &log_config, sizeof(Log_ConfigS));

    /* Get rti cfg. */
	if(PlatformCfg.LogCfg.rti_cfg == RTI_LOG_DISABLE)
	{
        rtiDynamicSwitch = FALSE;
	}

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      diagCommSdLogEnable                                              */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function check whether Diag To SD is enable or not.          */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
BOOL diagCommSdLogEnable(void)
{
#ifdef FAT32_FILE_SYSTEM
    if (!sdcard_is_ready())
    {
        return FALSE;
    }

    if (eeGetSystemAssertFlag())
	{
		return TRUE;
	}

	if (diagGetDevType() == DIAG_DEV_SD)
	{
        return TRUE;
	}
	else
#endif
	{
        return FALSE;
	}

}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      setDiagDevType                                                   */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function set diag device type.                               */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
void setDiagDevType(DIAG_DEV_TYPE type)
{
    DIAG_DEV_TYPE OrigType;

/*-------------------------------------------------------------------------*/

    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
    {
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }

    OrigType = PlatformCfg.diagCfg.diagDevType;
    if((OrigType == type)||(OrigType == DIAG_DEV_FS))
    {
        /* Platform Cfg Mutex Unlock */
        PlatformCfgMutexUnlock();
        return;
    }

    /* Set diag Dev Type */
    diagCfg.diagDevType = type;

	/* Diag config */
	memcpy(&(PlatformCfg.diagCfg), &diagCfg, sizeof(diagCfgDataS));

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    switch(OrigType)
    {
        case DIAG_DEV_SD:
        {
            diag_comm_sd_uninit();
            break;
        }

        default:
        {
            break;
        }
    }

    switch(type)
    {
        case DIAG_DEV_SD:
        {
            Usb_DriverS *pUsbDrvConfig = GetUsbDriverConfig();
            Usb3DwcDescriptorInfo *pUsbDesInfo = USB2GetDescriptorInfo();

            if(pUsbDrvConfig->mass_storage == MASS_STORAGE_ENABLE)
            {
                /* Reset current usb descriptor */
                pUsbDesInfo->CurrentDesc = USB_MAX_DESCRIPTOR_TYPE;

                pUsbDrvConfig->mass_storage = MASS_STORAGE_DISABLE;

                /* Usb simulate plug out/in */
                USB2MgrDeviceUnplugPlug();
            }

            diag_comm_sd_init();
            break;
        }

        default:
        {
            break;
        }
    }
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      eeConfiguration_SilentReset                                      */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function configure silent reset.                             */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
//ICAT EXPORTED FUNCTION - EE_HANDLER,EE,eeConfiguration_SilentReset
void eeConfiguration_SilentReset(FinalAct_t finalAction)
{
    /* Platform Cfg Mutex Lock */
    PlatformCfgMutexLock();

    if(!PlatformCfgReadFromNvm())
	{
         /* Set default platform configuration */
	    PlatformCfgSetDefaultSetting();
    }

    eeConfiguration.finalAction = finalAction;

    /* Copy EE Cfg Data */
    memcpy(&(PlatformCfg.eeCfg), &eeConfiguration, sizeof(EE_Configuration_t));

    /* Save platform configuration to NVM file. */
    PlatformCfgSaveToNvm();

    /* Platform Cfg Mutex Unlock */
    PlatformCfgMutexUnlock();

    OSATaskSleep(2);
}

#ifdef QUECTEL_PROJECT_CUST
// 获取 DUMP 模式
FinalAct_t quec_get_silent_mode(void)
{
    PlatformCfgMutexLock();
    if(!PlatformCfgReadFromNvm())
	{
         /* Set default platform configuration */
        PlatformCfgSetDefaultSetting();
    }
    memcpy(&(PlatformCfg.eeCfg), &eeConfiguration, sizeof(EE_Configuration_t));
    PlatformCfgMutexUnlock();
    return PlatformCfg.eeCfg.finalAction;
}
#endif
/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      SilentResetEnable                                                */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function enable silent reset.                               */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
//ICAT EXPORTED FUNCTION - EE_HANDLER,EE,SilentResetEnable
void SilentResetEnable(void)
{
	eeConfiguration_SilentReset(EE_RESET);
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      SilentResetDisable                                               */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function disable silent reset.                               */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
//ICAT EXPORTED FUNCTION - EE_HANDLER,EE,SilentResetDisable
void SilentResetDisable(void)
{
	eeConfiguration_SilentReset(EE_EXTERNAL);
}


#ifdef QUECTEL_PROJECT_CUST
#if 1 //axi monitor
#define  FAB_TIMEOUT_CTRL    0xD42A0060
#define  FAB_TIMEOUT_STAT0   0xD42A0068
#define  FAB_TIMEOUT_STAT1   0xD42A0070
#define  FAB_TIMEOUT_ID      0xD42A0078

void fab0_timeout_isr(void)
{
   unsigned int i; 
   unsigned int timeout_wraddr;
   unsigned int timeout_rdaddr;
   unsigned int reg1,reg2,reg3;

   RTI_LOG("=== enter fab0_timeout_isr()");

   INTCDisable(INTC_SRC_AXI_FABRIC_1_TO);

   //read 3 status registers and print it
   reg1 = BU_REG_READ(FAB_TIMEOUT_STAT0); //write
   reg2 = BU_REG_READ(FAB_TIMEOUT_STAT1); //read
   reg3 = BU_REG_READ(FAB_TIMEOUT_ID);
   RTI_LOG("FAB S2 TIMEOUT REG:0x%x, 0x%x, 0x%x",reg1,reg2,reg3);

   ASSERT(0);
   //INTCEnable(INTC_SRC_AXI_FABRIC_1_TO);
}



void fab0_timeout_init(void)
{

	RTI_LOG("fab0_timeout_init");
	
	 /* Enable fab0_timeout inerrupt. */
    INTCConfigure(INTC_SRC_AXI_FABRIC_1_TO, INTC_IRQ, INTC_HIGH_LEVEL);
    INTCBind(INTC_SRC_AXI_FABRIC_1_TO, (INTC_ISR)fab0_timeout_isr);


  	BU_REG_READ(FAB_TIMEOUT_CTRL) |= (0x1<<27);    //clear the fabric monitor value
 	BU_REG_READ(FAB_TIMEOUT_CTRL) &= ~(0x1<<28);   //rest the axi_fab_monitor
  	BU_REG_READ(FAB_TIMEOUT_CTRL) = 0xD3001000;    //[31]: autoresp_type=1, not error response, use interrupt
                                           //[30]: autoresp_en=1,   auto response enable
                                           //[29]: timeout_int_msk=0, interrupt enable
                                           //[28]: fab_mon_rst=1
                                           //[25]: no ready detect enable
                                           //[24]: new feature enable, axi monitor timeout response from axi_decoder slaves
                                           //[15:0]: time out is 0x1000*32=131072 cycle, axi_monitor counter clk is div32

    INTCEnable(INTC_SRC_AXI_FABRIC_1_TO);
} 
#endif

#if 1 //apb  monitor
#define  ARB_TIMEOUT1_EN_CLR 0xD42A0080
#define  ARB_TIMEOUT2_EN_CLR 0xD42A0088
#define  ARB_TIMEOUT1_STATUS 0xD42A0090
#define  ARB_TIMEOUT2_STATUS 0xD42A0098
#define  ARB_TIMEOUT1_ADDR   0xD42A00A0
#define  ARB_TIMEOUT2_ADDR   0xD42A00A8

UINT32 g_TIMEOUT1_STATUS;
UINT32 g_TIMEOUT1_ADDR;

#define TMR_REG    (0xD4080000)
#define MAX_REG_READ_CNT    (30)

void arb_timeout1_isr(UINT32 arg)
{
    unsigned k, rd_data;
    UINT32 v1=0;
    UINT32 tryCount=0;

    //read the status
    g_TIMEOUT1_STATUS  = BU_REG_READ(ARB_TIMEOUT1_STATUS);

    //read retry timeout address
    g_TIMEOUT1_ADDR  = BU_REG_READ(ARB_TIMEOUT1_ADDR);

    //clear the interrupt
    BU_REG_WRITE(ARB_TIMEOUT1_EN_CLR,0x1);    
    for(k=0; k<20; k++){}

    rd_data = BU_REG_READ(ARB_TIMEOUT1_EN_CLR);
    while(((rd_data&0x1)!=0x0) && (tryCount<MAX_REG_READ_CNT))
    {
       tryCount++;
       rd_data = BU_REG_READ(ARB_TIMEOUT1_EN_CLR);
    }
    if(tryCount>MAX_REG_READ_CNT)
        ASSERT(0);

    RTI_LOG("SVC R14: 0x%x\r\n",ReadMode_R14(CPSR_MODE_SVC));    
    RTI_LOG("ARB_TIMEOUT1_STATUS=0x%x, ARB_TIMEOUT1_ADDR=0x%x\r\n", g_TIMEOUT1_STATUS, g_TIMEOUT1_ADDR);

    ASSERT(0);
}

void arb_timeout_init(void)
{  
    unsigned int status,i;
    UINT32 cpsr;
	
	RTI_LOG("arb_timeout_init");

    INTCConfigure(INTC_SRC_ARB_TIMEOUT1, INTC_IRQ, INTC_HIGH_LEVEL);
    ASSERT(INTCBind(INTC_SRC_ARB_TIMEOUT1, arb_timeout1_isr) == INTC_RC_OK);
    INTCEnable(INTC_SRC_ARB_TIMEOUT1);
    BU_REG_WRITE(ARB_TIMEOUT1_EN_CLR, 0x1<<1);//enable timout1
}
#endif

#if 1 //cr5 error monitor
#define CR5_ERROR_RESP_CTRL (0xD4282C00 + 0x1DC)
unsigned int cr5_error_resp_status;
unsigned int g_CurrentTaskPC;
void isr_cr5_error_resp(UINT32 arg)
{
	cr5_error_resp_status = BU_REG_READ(CR5_ERROR_RESP_CTRL);
	RTI_LOG("isr_sg_resp_cp: status 0x%x, PC:0x%x", cr5_error_resp_status, g_CurrentTaskPC);
	ASSERT(0);
}

void cr5_error_resp_enable(void)
{
	int i;
	volatile unsigned int reg_val;

	reg_val = BU_REG_READ(CR5_ERROR_RESP_CTRL);
	reg_val |= (0xF << 1);
	BU_REG_WRITE(CR5_ERROR_RESP_CTRL, reg_val);

	for(i=0; i<50; i++);

	reg_val &= ~(0xF << 1);
	BU_REG_WRITE(CR5_ERROR_RESP_CTRL, reg_val);

	reg_val = BU_REG_READ(CR5_ERROR_RESP_CTRL);
	reg_val |= 0x1;
	BU_REG_WRITE(CR5_ERROR_RESP_CTRL, reg_val);

	RTI_LOG("%s, 0x%x", __func__, BU_REG_READ(CR5_ERROR_RESP_CTRL));
	
}

void cr5_error_resp_init(void)
{  
    unsigned int intNum = INTC_MAX_PRIMARY_INTERRUPT_SOURCES + 82;  //1606/1602/1605/1607/1609

	RTI_LOG("cr5_error_resp_init");

#if defined(PLAT_FALCON)
    intNum = INTC_MAX_PRIMARY_INTERRUPT_SOURCES + 24;  //1803
#else 
#if defined(PLAT_LAPWING)
	intNum = INTC_MAX_PRIMARY_INTERRUPT_SOURCES + 86;  //1903
#endif
#endif

    INTCConfigure((INTC_InterruptSources)intNum, INTC_IRQ, INTC_HIGH_LEVEL);
    INTCBind((INTC_InterruptSources)intNum, isr_cr5_error_resp);
    INTCEnable((INTC_InterruptSources)intNum);
	
	cr5_error_resp_enable();
}
#endif



#endif

