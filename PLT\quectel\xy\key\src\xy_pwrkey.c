#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include "quec_power.h"
#include "osa.h"
#include "xy_log.h"
#include "xy_pwrkey.h"

// 系统运行模式枚举

// 全局变量定义
static system_mode_e g_current_mode = SYSTEM_MODE_NORMAL;
static bool g_usb_connected = false;

#define PWRKEY_STACK_SIZE 0x2000
#define PWRKEY_TASK_PRIORITY 40
#define PWRKEY_TASK_NAME "power_key_task"

static OSATaskRef s_pwrkey_task = NULL;
static uint8_t s_pwrkey_stack[PWRKEY_STACK_SIZE];

static const char pwrkey_tag[] = "PWRKEY";

static int wifi_enable(bool enable)
{
    // TODO: 实现WiFi控制逻辑
    XY_LOGI(pwrkey_tag, "WiFi %s", enable ? "enabled" : "disabled");
    return 0;
}

static int mobile_data_enable(bool enable)
{
    // TODO: 实现移动数据控制逻辑
    XY_LOGI(pwrkey_tag, "Mobile data %s", enable ? "enabled" : "disabled");
    return 0;
}

static bool get_usb_connection_status(void)
{
    // TODO: 实现USB连接状态检测逻辑
    return g_usb_connected;
}

system_mode_e xy_pwrkey_get_mode(void)
{
    return g_current_mode;
}

static int pwrkey_set_mode(system_mode_e mode)
{
    if (g_current_mode == mode)
    {
        return 0;
    }

    bool network_enable;
    g_current_mode = mode;

    if (mode == SYSTEM_MODE_NORMAL)
    {
        network_enable = true;
        XY_LOGI(pwrkey_tag, "Enter NORMAL mode");
    }
    else
    {
        network_enable = false;
        XY_LOGI(pwrkey_tag, "Enter CHARGING mode");
    }

    int wifi_ret = wifi_enable(network_enable);
    int mobile_ret = mobile_data_enable(network_enable);

    if (wifi_ret != 0 || mobile_ret != 0)
    {
        XY_LOGE(pwrkey_tag, "Network configuration failed - WiFi:%d, Mobile:%d", wifi_ret, mobile_ret);
        return -1;
    }

    return 0;
}

static void pwrkey_cb(quec_power_down_mode_e event)
{
    switch (event)
    {
    case QUEC_POWER_MODE_POWER_DOWN_NORMAL:
        XY_LOGI(pwrkey_tag, "Long press shutdown trigger");

        if (g_current_mode == SYSTEM_MODE_NORMAL)
        {
            if (g_usb_connected)
            {
                pwrkey_set_mode(SYSTEM_MODE_CHARGING);
            }
            else
            {
                quec_power_control_entrance(1, QUEC_POWER_MODE_POWER_DOWN_NORMAL);
            }
        }
        else if (g_current_mode == SYSTEM_MODE_CHARGING)
        {
            pwrkey_set_mode(SYSTEM_MODE_NORMAL);
        }
        break;

    case QUEC_POWER_MODE_RESET:
        XY_LOGI(pwrkey_tag, "Reset trigger");
        break;

    case QUEC_POWER_MODE_POWER_DOWN_IMMEDIATE:
        XY_LOGI(pwrkey_tag, "IMMEDIATE shutdown trigger");
        break;

    case QUEC_POWER_MODE_SHORT_PRESSED:
        XY_LOGI(pwrkey_tag, "Short press trigger");
        break;

    default:
        XY_LOGI(pwrkey_tag, "Unknown power event: %d", event);
        break;
    }
}

static void pwrkey_task_entry(void *argv)
{
    int ret_key = 0;
    ql_pwrkey_type_e boot_type;

    boot_type = ql_power_get_pwk_type();
    XY_LOGI(pwrkey_tag, "Boot type: %d", boot_type);

    if (boot_type == QUEC_PWRKEY_EXTON1)
    {
        pwrkey_set_mode(SYSTEM_MODE_NORMAL);
    }
    else if (boot_type == QUEC_PWRKEY_EXTON2)
    {
        pwrkey_set_mode(SYSTEM_MODE_CHARGING);
        g_usb_connected = true;
    }
    else
    {
        XY_LOGE(pwrkey_tag, "Unknown boot type: %d", boot_type);
    }

    ql_set_power_manage_param(QUEC_PWR_KEY_TYPE_NOR, QUEC_PWRKEY_EXTON1);
    ql_set_power_manage_param(QUEC_PWR_WAIT_RELEASE, 1);
    ql_set_power_manage_param(QUEC_PWR_PRESS_TIME, 3000);
    ql_set_power_manage_param(QUEC_PWR_PRESS_SHUTDOWN, 0);

    ret_key = ql_pwrkey_register_callback(pwrkey_cb);
    if (ret_key != 0)
    {
        XY_LOGI(pwrkey_tag, "register failed!");
    }

    while (1)
    {
        bool current_usb_status = get_usb_connection_status();

        if (current_usb_status != g_usb_connected)
        {
            g_usb_connected = current_usb_status;

            if (g_current_mode == SYSTEM_MODE_CHARGING && !g_usb_connected)
            {
                XY_LOGI(pwrkey_tag, "USB disconnected in charging mode - shutting down");
                quec_power_control_entrance(1, QUEC_POWER_MODE_POWER_DOWN_NORMAL);
            }
        }

        OSATaskSleep(100);
    }
}

int xy_pwrkey_init(void)
{
    if (s_pwrkey_task)
    {
        XY_LOGI(pwrkey_tag, "Already initialized");
        return 0;
    }

    if (OSATaskCreate(&s_pwrkey_task, s_pwrkey_stack,
                      PWRKEY_STACK_SIZE, PWRKEY_TASK_PRIORITY,
                      PWRKEY_TASK_NAME, pwrkey_task_entry, NULL) != OS_SUCCESS)
    {
        XY_LOGI(pwrkey_tag, "Create pwrkey task fail");
        return -1;
    }

    XY_LOGI(pwrkey_tag, "Init pwrkey module");

    return 0;
}
